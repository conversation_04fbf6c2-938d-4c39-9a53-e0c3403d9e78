{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "ConcertInsightsVisualizer", "Description": "Analyses and provides visualization widgets for Concert message types in Unreal Insights.", "Category": "Networking", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "CanContainContent": false, "Hidden": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "SupportedPrograms": ["UnrealInsights"], "Modules": [{"Name": "ConcertInsightsVisualizer", "Type": "EditorAndProgram", "LoadingPhase": "<PERSON><PERSON><PERSON>", "ProgramAllowList": ["UnrealInsights"]}]}