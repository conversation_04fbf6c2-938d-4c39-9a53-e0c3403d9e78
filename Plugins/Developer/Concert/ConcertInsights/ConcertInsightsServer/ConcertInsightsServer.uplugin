{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "ConcertInsightsServer", "Description": "Listens for requests of clients to start synchronized tracing, which is initiated in the ConcertInsightsClient plugin.", "Category": "Networking", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "CanContainContent": true, "Hidden": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "SupportedPrograms": ["UnrealMultiUserServer", "UnrealMultiUserSlateServer"], "Modules": [{"Name": "ConcertInsightsServer", "Type": "Program", "LoadingPhase": "<PERSON><PERSON><PERSON>", "ProgramAllowList": ["UnrealMultiUserServer", "UnrealMultiUserSlateServer"]}], "Plugins": [{"Name": "ConcertInsightsCore", "Enabled": true}, {"Name": "ConcertSyncServer", "Enabled": true}]}