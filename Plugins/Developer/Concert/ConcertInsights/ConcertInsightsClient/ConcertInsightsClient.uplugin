{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "ConcertInsightsClient", "Description": "Extends status bar so you can start a synchronized trace on all connected Concert endpoints.", "Category": "Networking", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "CanContainContent": true, "Hidden": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "ConcertInsightsClient", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "ConcertInsightsCore", "Enabled": true}, {"Name": "ConcertSharedSlate", "Enabled": true}, {"Name": "ConcertSyncClient", "Enabled": true}, {"Name": "TraceUtilities", "Enabled": true}]}