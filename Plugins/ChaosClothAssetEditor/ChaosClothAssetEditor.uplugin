{"FileVersion": 1, "Version": 1, "VersionName": "0.1", "FriendlyName": "Chaos Cloth Asset Editor", "Description": "Editor for modifying cloth assets", "Category": "Other", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": true, "Installed": false, "Modules": [{"Name": "ChaosClothAssetEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "ChaosClothAssetEditorTools", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "ChaosClothAssetDataflowNodes", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "ChaosClothAssetTools", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}], "Plugins": [{"Name": "GeometryProcessing", "Enabled": true}, {"Name": "ChaosClothAsset", "Enabled": true}, {"Name": "ChaosCloth", "Enabled": true}, {"Name": "MeshModelingToolsetExp", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "BaseCharacterFXEditor", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "Dataflow", "Enabled": true}, {"Name": "USDImporter", "Enabled": true, "SupportedTargetPlatforms": ["Win64", "<PERSON>", "Linux"]}]}