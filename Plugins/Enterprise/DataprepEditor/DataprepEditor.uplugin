{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Dataprep Editor", "Description": "A tool to simplify creation and execution of data preparation pipelines from within the Unreal Editor.", "Category": "Dataprep", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "DataprepEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DataprepCore", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DataprepLibraries", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DataprepEditorScriptingUtilities", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "DatasmithContent", "Enabled": true}, {"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "VariantManager<PERSON><PERSON>nt", "Enabled": true}, {"Name": "Interchange", "Enabled": true, "SupportedTargetPlatforms": ["Win64", "Linux", "<PERSON>"]}], "IsExperimentalVersion": false}