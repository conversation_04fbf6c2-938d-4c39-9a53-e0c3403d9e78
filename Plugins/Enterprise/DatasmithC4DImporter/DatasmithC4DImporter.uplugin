{"FileVersion": 3, "Version": 2, "VersionName": "1.1", "FriendlyName": "Datasmith C4D Importer", "Description": "Adds support for importing content from Cinema4D applications into Unreal Engine", "Category": "Importers", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "https://docs.unrealengine.com/en-US/WorkingWithContent/Importing/Datasmith/", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "SupportedTargetPlatforms": ["Win64", "<PERSON>"], "Modules": [{"Name": "DatasmithC4DTranslator", "Type": "Runtime", "LoadingPhase": "PostDefault", "PlatformAllowList": ["Win64", "<PERSON>"]}], "Plugins": [{"Name": "DatasmithImporter", "Enabled": true}]}