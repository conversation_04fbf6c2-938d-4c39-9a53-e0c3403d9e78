{"FileVersion": 3, "Version": 1, "VersionName": "1", "FriendlyName": "Asset Referencing Restrictions", "Description": "Apply project-specific restrictions to how content in different folders or plugins can be referenced", "Category": "Editor", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "AssetReferenceRestrictions", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "DataValidation", "Enabled": true}]}