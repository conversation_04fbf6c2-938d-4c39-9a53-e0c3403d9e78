{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "<PERSON><PERSON><PERSON>", "Description": "Use Chooser and Proxy Tables to build dynamic asset selection logic.", "Category": "Animation", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "Installed": false, "Plugins": [{"Name": "GameplayTagsEditor", "Enabled": true}, {"Name": "BlendStack", "Enabled": true}, {"Name": "GameplayInsights", "Enabled": true}], "Modules": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ProxyTable", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ProxyTableUncooked", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ProxyTableEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}]}