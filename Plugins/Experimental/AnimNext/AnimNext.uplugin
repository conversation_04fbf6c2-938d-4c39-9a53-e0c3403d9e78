{"FileVersion": 3, "Version": 1, "VersionName": "0.1", "FriendlyName": "<PERSON><PERSON>", "Description": "Framework for defining functional data flow for animation systems", "Category": "Animation", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Plugins": [{"Name": "ControlRig", "Enabled": true}, {"Name": "ContentBrowserFileDataSource", "Enabled": true}, {"Name": "PythonScriptPlugin", "Enabled": true}, {"Name": "Workspace", "Enabled": true}, {"Name": "EditorScriptingUtilities", "Enabled": true, "TargetAllowList": ["Editor"]}], "Modules": [{"Name": "AnimNext", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "AnimNextEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AnimNextUncookedOnly", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AnimNextTestSuite", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}]}