{"FileVersion": 1, "Version": 1, "VersionName": "1.0", "FriendlyName": "Editor Telemetry", "Description": "Plugin that emits common telemetry events from the Editor", "Category": "Telemetry", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": true, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "EditorTelemetry", "Type": "Editor", "LoadingPhase": "PostConfigInit"}], "Plugins": [{"Name": "StudioTelemetry", "Enabled": true}]}