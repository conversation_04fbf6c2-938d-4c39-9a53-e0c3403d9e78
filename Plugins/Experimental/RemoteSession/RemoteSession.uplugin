{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "RemoteSession", "Description": "A plugin for Unreal that allows one instance to act as a thin-client (rendering and input) to a second instance", "Category": "Experimental", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "Installed": false, "EnabledByDefault": false, "Modules": [{"Name": "RemoteSession", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformDenyList": ["TVOS"]}, {"Name": "RemoteSessionEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformDenyList": ["TVOS"]}], "Plugins": [{"Name": "BackChannel", "Enabled": true}, {"Name": "AppleImageUtils", "Enabled": true}, {"Name": "MediaIOFramework", "Enabled": true}, {"Name": "XRBase", "Enabled": true}]}