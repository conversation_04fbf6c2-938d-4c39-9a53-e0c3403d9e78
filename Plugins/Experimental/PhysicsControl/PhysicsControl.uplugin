{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "PhysicsControl", "Description": "Additional Support for controlling static and skeletal meshes through physical controls", "Category": "Physics", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "CanContainVerse": false, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "PhysicsControl", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "PhysicsControlUncookedOnly", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "PhysicsControlEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit"}]}