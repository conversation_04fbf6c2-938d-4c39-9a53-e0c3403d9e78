{"FileVersion": 3, "Version": 1, "VersionName": "0.1", "FriendlyName": "Experimental Mesh Modeling Toolset", "Description": "A set of experimental modules implementing 3D mesh creation and editing based on the Interactive Tools Framework", "Category": "Other", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsExperimentalVersion": true, "Installed": false, "Hidden": true, "Modules": [{"Name": "MeshModelingToolsExp", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "MeshModelingToolsEditorOnlyExp", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "GeometryProcessingAdapters", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "ModelingEditorUI", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ModelingUI", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "SkeletalMeshModifiers", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "MeshModelingToolset", "Enabled": true}, {"Name": "GeometryProcessing", "Enabled": true}, {"Name": "ProxyLODPlugin", "Enabled": true}]}