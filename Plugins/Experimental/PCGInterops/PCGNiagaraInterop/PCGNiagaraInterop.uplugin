{"FileVersion": 3, "Version": 1, "VersionName": "0.1", "FriendlyName": "Procedural Content Generation Framework (PCG) Niagara Interop", "Description": "Extra plugin for Procedural Content Generation Framework interacting with the Niagara system.", "Category": "Editor", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "https://docs.unrealengine.com/latest/en-US/procedural-content-generation--framework-in-unreal-engine/", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "PCGNiagaraInterop", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "PCG", "Enabled": true}, {"Name": "Niagara", "Enabled": true}]}