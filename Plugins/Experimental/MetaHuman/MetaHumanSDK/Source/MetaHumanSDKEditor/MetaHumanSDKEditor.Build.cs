// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;

public class MetaHumanSDKEditor : ModuleRules
{
	public MetaHumanSDKEditor(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
		
		PublicIncludePaths.AddRange(
			new string[] {
				// ... add public include paths required here ...
			}
			);
				
		
		PrivateIncludePaths.AddRange(
			new string[] {
				// ... add other private include paths required here ...
			}
			);
			
		
		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				// ... add other public dependencies that you statically link with here ...
			}
			);
			
		
		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"ApplicationCore",
				"CoreUObject",
				"EditorScriptingUtilities",
				"Engine",
				"HTTP",
				"InputCore",
				"Json",
				"JsonUtilities",
				"Projects",
				"Slate",
				"SlateCore",
				"ToolWidgets",
				"UnrealEd",
				"HairStrandsCore",
				"RigLogicModule",
				"RigVMDeveloper",
				"ControlRig",
				"ControlRigDeveloper",
			}
			);
		
		
		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
				// ... add any modules that your module loads dynamically here ...
			}
			);
	}
}
