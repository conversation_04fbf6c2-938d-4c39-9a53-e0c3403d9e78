{"FileVersion": 3, "Version": 1, "VersionName": "0.1", "FriendlyName": "Groom Extensions For Mutable", "Description": "Adds Mutable functionality to work with Grooms from the HairStrands plugin", "Category": "Animation", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": false, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "EnabledByDefault": false, "Modules": [{"Name": "HairStrandsMutable", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HairStrandsMutableEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "HairStrands", "Enabled": true}, {"Name": "Mutable", "Enabled": true}, {"Name": "StructUtils", "Enabled": true}]}