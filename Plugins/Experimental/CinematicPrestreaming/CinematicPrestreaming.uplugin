{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Cinematic Prestreaming", "Description": "Adds a way to record certain types of streaming data requests in cinematic cutscenes. The requests can then be played back in advance on the Sequencer timeline to pre-stream data during normal gameplay/rendering.", "Category": "Rendering", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "CinematicPrestreaming", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "CinematicPrestreamingEditor", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "MovieRenderPipeline", "Enabled": true}]}