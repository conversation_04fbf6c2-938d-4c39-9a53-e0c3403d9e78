{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Cloners and Effectors", "Description": "Niagara based cloner system with various layouts and effector affecting each clone instances", "Category": "Experimental", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ClonerEffectorEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ClonerEffectorMeshBuilder", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "GeometryScripting", "Enabled": true}, {"Name": "MeshModelingToolsetExp", "Enabled": true}, {"Name": "ProceduralMeshComponent", "Enabled": true}, {"Name": "Niagara", "Enabled": true}, {"Name": "NiagaraSimCaching", "Enabled": true}]}