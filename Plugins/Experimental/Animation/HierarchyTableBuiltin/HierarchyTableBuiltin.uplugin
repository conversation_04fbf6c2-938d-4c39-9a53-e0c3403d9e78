{"FileVersion": 3, "Version": 1, "VersionName": "0.1", "FriendlyName": "Hierarchy Table Builtin", "Description": "Contains built-in types for controlling animation blending", "Category": "Animation", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "HierarchyTableBuiltinRuntime", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HierarchyTableBuiltinEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HierarchyTableBuiltinUncookedOnly", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "HierarchyTable", "Enabled": true}, {"Name": "EditorDataStorageFeatures", "Enabled": true}]}