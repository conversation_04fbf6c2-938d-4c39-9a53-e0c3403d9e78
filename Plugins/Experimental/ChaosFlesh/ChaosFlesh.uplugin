{"FileVersion": 3, "Version": 1, "VersionName": "0.1", "FriendlyName": "Chaos Flesh", "Description": "Chaos Flesh Simulation", "Category": "Physics", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "http://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "ChaosFlesh", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ChaosFleshEngine", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ChaosFleshEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ChaosFleshNodes", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "ChaosFleshDeprecatedNodes", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}], "Plugins": [{"Name": "ProceduralMeshComponent", "Enabled": true}, {"Name": "Dataflow", "Enabled": true}, {"Name": "ChaosCaching", "Enabled": true}, {"Name": "ChaosCachingUSD", "Enabled": true, "SupportedTargetPlatforms": ["Win64"]}, {"Name": "GeometryProcessing", "Enabled": true}, {"Name": "ComputeFramework", "Enabled": true}, {"Name": "DeformerGraph", "Enabled": true}, {"Name": "USDImporter", "Enabled": true, "SupportedTargetPlatforms": ["Win64"]}, {"Name": "TetMeshing", "Enabled": true}]}