{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Apple Vision API", "Description": "Allows you to issue computer vision api calls for textures (camera data or render targets)", "Category": "Experimental", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": true, "Installed": false, "Modules": [{"Name": "AppleVision", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformDenyList": ["TVOS"]}, {"Name": "AppleVisionBlueprintSupport", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}], "Plugins": [{"Name": "AppleImageUtils", "Enabled": true}]}