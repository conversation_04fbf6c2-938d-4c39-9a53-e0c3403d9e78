{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Motion Design", "Description": "Compositing, designer and broadcasting tool.\n\nPlugin Dependencies: Advanced Renamer, Custom Details View, Dynamic Material, Geometry Cache, Geometry Scripting, Media Compositing, Media IO Framework, Mesh Modeling Toolset Exp, Remote Control, SVG Importer, Text3D and ActorModifierCore.", "Category": "Experimental", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "EnabledByDefault": false, "SupportedTargetPlatforms": ["Win64", "Linux", "<PERSON>"], "Modules": [{"Name": "Avalanche", "Type": "RuntimeAndProgram", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheCore", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheCamera", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheEditorCore", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheComponentVisualizers", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheEffectors", "Type": "RuntimeAndProgram", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheEffectorsEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheInteractiveTools", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheInteractiveToolsRuntime", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheLevelViewport", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheOutliner", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheMask", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheMaskEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheMedia", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "TargetDenyList": ["Server"]}, {"Name": "AvalancheMediaEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheModifiers", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheModifiersEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalanchePropertyAnimator", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalanchePropertyAnimatorEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheRemoteControl", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheRemoteControlEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheSequence", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheSequencer", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheShapes", "Type": "RuntimeAndProgram", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheShapesEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheText", "Type": "RuntimeAndProgram", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheTextEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheViewport", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheSceneTree", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheTransition", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheTransitionEditor", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheMRQ", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheMRQEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheSVGEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheTag", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheTagEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheAttribute", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetDenyList": ["Server"]}, {"Name": "AvalancheAttributeEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheSceneRig", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AvalancheSceneRigEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "AdvancedRenamer", "Enabled": true}, {"Name": "CustomDetailsView", "Enabled": true}, {"Name": "DynamicMaterial", "Enabled": true}, {"Name": "GeometryCache", "Enabled": true}, {"Name": "GeometryScripting", "Enabled": true}, {"Name": "LevelSequenceEditor", "Enabled": true}, {"Name": "MediaCompositing", "Enabled": true}, {"Name": "MediaIOFramework", "Enabled": true}, {"Name": "MediaPlate", "Enabled": true}, {"Name": "MeshModelingToolsetExp", "Enabled": true}, {"Name": "MovieRenderPipeline", "Enabled": true}, {"Name": "ProceduralMeshComponent", "Enabled": true}, {"Name": "PropertyAnimator", "Enabled": true}, {"Name": "PropertyAnimatorCore", "Enabled": true}, {"Name": "RemoteControl", "Enabled": true}, {"Name": "RemoteControlComponents", "Enabled": true}, {"Name": "SerializationUtils", "Enabled": true}, {"Name": "StateTree", "Enabled": true}, {"Name": "SVGImporter", "Enabled": true}, {"Name": "Text3D", "Enabled": true}, {"Name": "ActorModifierCore", "Enabled": true}, {"Name": "OperatorStack", "Enabled": true}, {"Name": "GeometryMask", "Enabled": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Enabled": true}]}