{"FileVersion": 1, "Version": 1, "VersionName": "0.1", "FriendlyName": "Gameplay Cameras", "Description": "A modular and data-driven camera system for Unreal", "Category": "Cameras", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": true, "CanContainContent": true, "IsExperimentalVersion": true, "Installed": false, "Plugins": [{"Name": "EnhancedInput", "Enabled": true}, {"Name": "StateTree", "Enabled": true}, {"Name": "TemplateSequence", "Enabled": true}], "Modules": [{"Name": "GameplayCameras", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "GameplayCamerasUncookedOnly", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "GameplayCamerasEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit"}]}